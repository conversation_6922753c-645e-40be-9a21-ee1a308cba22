{"name": "@notionhq/notion-mcp-server", "keywords": ["notion", "api", "mcp", "server"], "version": "1.6.0", "license": "MIT", "type": "module", "scripts": {"build": "tsc -build && node scripts/build-cli.js", "dev": "tsx watch scripts/start-server.ts"}, "bin": {"notion-mcp-server": "bin/cli.mjs"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.8.0", "axios": "^1.8.4", "express": "^4.21.2", "form-data": "^4.0.1", "mustache": "^4.2.0", "openapi-client-axios": "^7.5.5", "openapi-schema-validator": "^12.1.3", "openapi-types": "^12.1.3", "which": "^5.0.0", "zod": "3.24.1"}, "devDependencies": {"@anthropic-ai/sdk": "^0.33.1", "@types/express": "^5.0.0", "@types/js-yaml": "^4.0.9", "@types/json-schema": "^7.0.15", "@types/mustache": "^4.2.5", "@types/node": "^20.17.16", "@types/which": "^3.0.4", "@vitest/coverage-v8": "3.1.1", "esbuild": "^0.25.2", "multer": "1.4.5-lts.1", "openai": "^4.91.1", "tsx": "^4.19.3", "typescript": "^5.8.2", "vitest": "^3.1.1"}, "description": "Official MCP server for Notion API", "main": "index.js", "repository": {"type": "git", "url": "**************:makenotion/notion-mcp-server.git"}, "author": "@notionhq", "bugs": {"url": "https://github.com/makenotion/notion-mcp-server/issues"}, "homepage": "https://github.com/makenotion/notion-mcp-server#readme"}