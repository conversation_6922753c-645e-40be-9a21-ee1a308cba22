{"compilerOptions": {"composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./build", "target": "es2021", "lib": ["es2022"], "jsx": "react-jsx", "module": "es2022", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "resolveJsonModule": true, "allowJs": true, "checkJs": false, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true}, "include": ["test/**/*.ts", "scripts/**/*.ts", "src/**/*.ts"]}