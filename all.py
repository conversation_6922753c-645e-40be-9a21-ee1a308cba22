# All Notion MCP Server Tools
# These are the tools available when you connect this MCP server to Claude/Curs<PERSON>

# USER MANAGEMENT
def get_user(user_id: str):
    """Retrieve a user by ID"""
    pass

def list_users(start_cursor: str = None, page_size: int = 100):
    """List all users in the workspace"""
    pass

def get_bot_user():
    """Retrieve the bot user associated with the integration"""
    pass

# DATABASE OPERATIONS
def list_databases(start_cursor: str = None, page_size: int = 100):
    """List all databases accessible to the integration"""
    pass

def get_database(database_id: str):
    """Retrieve database metadata including properties and schema"""
    pass

def query_database(
    database_id: str,
    filter: dict = None,
    sorts: list = None,
    start_cursor: str = None,
    page_size: int = 100
):
    """Query database with filters, sorts, and pagination"""
    pass

def create_database(
    parent: dict,
    title: list,
    properties: dict,
    description: list = None,
    icon: dict = None,
    cover: dict = None,
    is_inline: bool = False
):
    """Create a new database"""
    pass

def update_database(
    database_id: str,
    title: list = None,
    description: list = None,
    properties: dict = None,
    icon: dict = None,
    cover: dict = None,
    is_inline: bool = None
):
    """Update database properties and metadata"""
    pass

# PAGE OPERATIONS
def get_page(page_id: str):
    """Retrieve a page by ID"""
    pass

def create_page(
    parent: dict,
    properties: dict = None,
    children: list = None,
    icon: dict = None,
    cover: dict = None
):
    """Create a new page in database or as child page"""
    pass

def update_page(
    page_id: str,
    properties: dict = None,
    archived: bool = None,
    icon: dict = None,
    cover: dict = None
):
    """Update page properties and metadata"""
    pass

def get_page_property(page_id: str, property_id: str):
    """Retrieve a specific property from a page"""
    pass

# BLOCK OPERATIONS
def get_block(block_id: str):
    """Retrieve a block by ID"""
    pass

def get_block_children(
    block_id: str,
    start_cursor: str = None,
    page_size: int = 100
):
    """Retrieve children blocks of a parent block"""
    pass

def append_block_children(block_id: str, children: list):
    """Append new blocks as children to a parent block"""
    pass

def update_block(
    block_id: str,
    paragraph: dict = None,
    heading_1: dict = None,
    heading_2: dict = None,
    heading_3: dict = None,
    bulleted_list_item: dict = None,
    numbered_list_item: dict = None,
    to_do: dict = None,
    toggle: dict = None,
    code: dict = None,
    child_page: dict = None,
    child_database: dict = None,
    embed: dict = None,
    image: dict = None,
    video: dict = None,
    file: dict = None,
    pdf: dict = None,
    bookmark: dict = None,
    callout: dict = None,
    quote: dict = None,
    equation: dict = None,
    divider: dict = None,
    table_of_contents: dict = None,
    column: dict = None,
    column_list: dict = None,
    link_preview: dict = None,
    synced_block: dict = None,
    template: dict = None,
    link_to_page: dict = None,
    table: dict = None,
    table_row: dict = None,
    archived: bool = None
):
    """Update a block's content and properties"""
    pass

def delete_block(block_id: str):
    """Delete a block"""
    pass

# COMMENT OPERATIONS
def get_comments(
    block_id: str,
    start_cursor: str = None,
    page_size: int = 100
):
    """Retrieve comments for a block"""
    pass

def create_comment(
    parent: dict,
    rich_text: list,
    discussion_id: str = None
):
    """Create a comment on a page or block"""
    pass

# SEARCH OPERATIONS
def search(
    query: str = None,
    sort: dict = None,
    filter: dict = None,
    start_cursor: str = None,
    page_size: int = 100
):
    """Search across all pages and databases"""
    pass

# EXAMPLE USAGE PATTERNS:

# 1. Get database schema first
get_database("your-database-id")

# 2. Query database with filters
query_database(
    database_id="your-database-id",
    filter={
        "property": "Status",
        "select": {"equals": "In Progress"}
    },
    sorts=[{
        "property": "Created",
        "direction": "descending"
    }]
)

# 3. Create a new page
create_page(
    parent={"database_id": "your-database-id"},
    properties={
        "Name": {"title": [{"text": {"content": "New Project"}}]},
        "Status": {"select": {"name": "Not Started"}},
        "Priority": {"select": {"name": "High"}},
        "Due Date": {"date": {"start": "2025-01-15"}}
    }
)

# 4. Add content blocks to a page
append_block_children(
    block_id="page-id",
    children=[
        {
            "type": "heading_2",
            "heading_2": {
                "rich_text": [{"text": {"content": "Tasks"}}]
            }
        },
        {
            "type": "to_do",
            "to_do": {
                "rich_text": [{"text": {"content": "Complete project setup"}}],
                "checked": False
            }
        }
    ]
)

# 5. Search for pages
search(
    query="project",
    filter={"property": "object", "value": "page"}
)

# 6. Update page properties
update_page(
    page_id="page-id",
    properties={
        "Status": {"select": {"name": "Completed"}},
        "Progress": {"number": 100}
    }
)