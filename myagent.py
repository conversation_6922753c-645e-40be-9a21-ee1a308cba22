"""
LangChain Workflow Implementation
Converted from <PERSON>py<PERSON> notebook to Python script
"""

import os
from typing import Literal, Optional, List, Dict
from dotenv import load_dotenv
from pydantic import BaseModel, Field
import requests
import json

from langchain.chat_models import init_chat_model
from langchain.tools import tool
from langgraph.graph import MessagesState, StateGraph, START, END
from langgraph.types import Command
from TelegramAi.prompts import triage_system_prompt, default_background, default_triage_instructions , triage_user_prompt, AGENT_TOOLS_PROMPT, default_response_preferences, agent_system_prompt
from rich.markdown import Markdown
from langgraph.graph import END

from main import TeleBot, get_client
from telethon import events

# Load environment variables
load_dotenv(".env", override=True)


token = os.getenv("NOTION_API_KEY")
databaseID = os.getenv("DATABASE_ID")

headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json",
    "Notion-Version": "2022-02-22"
}


# Initialize LLM
llm = init_chat_model("gemini-2.5-pro", model_provider="google_genai")


class State(MessagesState):
    """State class for the workflow"""
    # We can add a specific key to our state for the email input
    post_input: dict
    classification_decision: Literal["ignore", "notify", "take_action"]


# Router schema
class RouterSchema(BaseModel):
    """ Analyze the post and route it according to its content."""

    reasoning: str = Field(
        description="Step-by-step reasoning behind the classification."
    )
    classification: Literal["ignore", "notify", "take_action"] = Field(
        description="The classification of an post: 'ignore' for irrelevant post, "
        "'notify' for important information that doesn't require response, "
        "'take_action' for post that requires a action like to create a task, create a page",
    )
    

# Create router LLM with structured output
llm_router = llm.with_structured_output(RouterSchema)


def triage_router(state: State) -> Command[Literal["action_agent", "__end__"]]:
    """Analyze post content to decide if we should create a task, notify, or ignore."""
    
    # Parse the post input
    post_input = state["post_input"]
    text = post_input.get("text", "")
    metadata = post_input.get("metadata", {})
    system_prompt = triage_system_prompt.format(
        triage_instructions=default_triage_instructions
    )
    
    user_prompt = triage_user_prompt.format(
        metadata=metadata,
        post_thread=text
    )
    
    # Run the router LLM
    result = llm_router.invoke(
        [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": text},
        ]
    )
    print(result)
    # Decision
    if result.classification == "take_action":
        print("📧 Classification: Take Action - This post requires to create a task")
        goto = "action_agent"
        update = {
            "messages": [
                {
                    "role": "user",
                    "content": f"Take action for this post: \n\n{user_prompt}",
                }
            ],
            "classification_decision": result.classification,
        }
    
    elif result.classification == "ignore":
        print("🚫 Classification: IGNORE - This post can be safely ignored")
        goto = END
        update =  {
            "classification_decision": result.classification,
        }
    
    elif result.classification == "notify":
        print("🔔 Classification: NOTIFY - This post contains important information")
        goto = END
        update = {
            "classification_decision": result.classification,
        }
    else:
        raise ValueError(f"Invalid classification: {result.classification}")
    return Command(goto=goto, update=update)

# Define tools

@tool
def notify_user(message: str, urgency: str) -> str:
    """Notify the user about important updates or task creation."""
    return f"User notified with message: {message} and urgency: {urgency}"

@tool
def read_database_metadata():
    """Get database schema with all properties and their types. Call this FIRST."""
    url = f"https://api.notion.com/v1/databases/{databaseID}"  # Fixed endpoint
    res = requests.get(url, headers=headers)
    data = res.json()
    
    # Extract and format property information for AI understanding
    if res.status_code == 200 and 'properties' in data:
        formatted_info = {
            "database_id": data.get("id"),
            "title": data.get("title", [{}])[0].get("plain_text", ""),
            "properties": {}
        }
        
        for prop_name, prop_data in data["properties"].items():
            prop_type = prop_data.get("type")
            formatted_info["properties"][prop_name] = {
                "type": prop_type,
                "id": prop_data.get("id")
            }
            
            # Add options for select/multi_select
            if prop_type in ["select", "multi_select"] and prop_type in prop_data:
                options = prop_data[prop_type].get("options", [])
                formatted_info["properties"][prop_name]["options"] = [
                    opt["name"] for opt in options
                ]
        
        return {"status": res.status_code, "schema": formatted_info, "raw": data}
    
    return {"status": res.status_code, "data": data}

@tool
def retrive_database_data(
    filter: Optional[Dict] = None,
    sorts: Optional[List[Dict]] = None,
    start_cursor: Optional[str] = None,
    page_size: Optional[int] = 100
):
    """
    Read data from the notion database with advanced filtering and sorting.
    
    Args:
        filter: Filter conditions (e.g., {"property": "Status", "select": {"equals": "Done"}})
        sorts: Sort criteria (e.g., [{"property": "Created", "direction": "descending"}])
        start_cursor: Pagination cursor for next page
        page_size: Number of items per page (max 100)
    """
    payload = {}
    if filter:
        payload["filter"] = filter
    if sorts:
        payload["sorts"] = sorts
    if start_cursor:
        payload["start_cursor"] = start_cursor
    if page_size:
        payload["page_size"] = page_size
    
    url = f"https://api.notion.com/v1/databases/{databaseID}/query"
    res = requests.post(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def create_page(
    title: str,
    properties: Optional[Dict] = None,
    children: Optional[List[Dict]] = None
):
    """
    Create a new page in the notion database.
    
    Args:
        title: Page title
        properties: Additional properties (e.g., {"Status": {"select": {"name": "In Progress"}}})
        children: Initial content blocks
    """
    # Build the title property
    title_property = {
        "title": [{"text": {"content": title}}]
    }
    
    # Merge with additional properties
    all_properties = {**title_property, **(properties or {})}
    
    payload = {
        "parent": {"database_id": databaseID},
        "properties": all_properties
    }
    
    if children:
        payload["children"] = children
    
    url = 'https://api.notion.com/v1/pages'
    res = requests.post(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def upadate_page(
    page_id: str,
    properties: Optional[Dict] = None,
    archived: Optional[bool] = None,
    cover: Optional[Dict] = None,
    icon: Optional[Dict] = None
):
    """
    Update a page in the notion database.
    
    Args:
        page_id: ID of the page to update
        properties: Properties to update
        archived: Whether to archive the page
        cover: Cover image object
        icon: Icon object
    """
    payload = {}
    if properties:
        payload["properties"] = properties
    if archived is not None:
        payload["archived"] = archived
    if cover:
        payload["cover"] = cover
    if icon:
        payload["icon"] = icon
    
    url = f"https://api.notion.com/v1/pages/{page_id}"
    res = requests.patch(url, headers=headers, json=payload)
    
    # Enhanced error handling
    if res.status_code >= 400:
        error_data = res.json()
        return {
            "status": res.status_code,
            "error": error_data.get("message", "Unknown error"),
            "code": error_data.get("code"),
            "data": error_data
        }
    
    return {"status": res.status_code, "data": res.json()}

@tool
def create_block(page_id: str, children: List[Dict]):
    """Create new blocks in a notion page"""
    url = f"https://api.notion.com/v1/blocks/{page_id}/children"
    payload = {"children": children}
    
    res = requests.patch(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def update_block(block_id: str, payload: dict):
    """Update a block in the notion page."""
    url = f"https://api.notion.com/v1/blocks/{block_id}"
    res = requests.patch(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def retreve_block_children(
    block_id: str,
    start_cursor: Optional[str] = None,
    page_size: int = 100
):
    """Retrieve all children of a block with pagination"""
    url = f"https://api.notion.com/v1/blocks/{block_id}/children"
    params = {"page_size": page_size}
    if start_cursor:
        params["start_cursor"] = start_cursor
    
    res = requests.get(url, headers=headers, params=params)
    return {"status": res.status_code, "data": res.json()}

@tool
def search_pages(
    query: Optional[str] = None,
    filter_value: Optional[str] = None,
    sort_direction: str = "descending",
    sort_timestamp: str = "last_edited_time",
    start_cursor: Optional[str] = None,
    page_size: int = 100
):
    """
    Search across all pages and databases.
    
    Args:
        query: Text to search for
        filter_value: Filter by object type ("page" or "database")
        sort_direction: "ascending" or "descending"
        sort_timestamp: "last_edited_time" or "created_time"
        start_cursor: Pagination cursor
        page_size: Results per page
    """
    payload = {
        "sort": {
            "direction": sort_direction,
            "timestamp": sort_timestamp
        },
        "page_size": page_size
    }
    
    if query:
        payload["query"] = query
    if filter_value:
        payload["filter"] = {"value": filter_value, "property": "object"}
    if start_cursor:
        payload["start_cursor"] = start_cursor
    
    url = "https://api.notion.com/v1/search"
    res = requests.post(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def create_page_with_validation(payload: dict):
    """Create a new page with schema validation."""
    # First get database metadata to validate
    metadata_result = read_database_metadata()
    if metadata_result["status"] != 200:
        return {"error": "Could not fetch database schema", "status": 400}
    
    schema = metadata_result["schema"]["properties"]
    
    # Validate payload against schema
    validated_payload = {}
    for prop_name, prop_value in payload.items():
        if prop_name not in schema:
            return {"error": f"Property '{prop_name}' not found in database schema", "status": 400}
        validated_payload[prop_name] = prop_value
    
    # Create the page
    full_payload = {
        "parent": {"database_id": databaseID},
        "properties": validated_payload
    }
    
    url = 'https://api.notion.com/v1/pages'
    res = requests.post(url, headers=headers, json=full_payload)
    return {"status": res.status_code, "data": res.json()}

@tool
def create_task_block(page_id: str, task_name: str, checked: bool = False):
    """Create a to-do block in a page."""
    payload = {
        "children": [{
            "object": "block",
            "type": "to_do",
            "to_do": {
                "rich_text": [{"type": "text", "text": {"content": task_name}}],
                "checked": checked,
                "color": "default"
            }
        }]
    }
    
    url = f"https://api.notion.com/v1/blocks/{page_id}/children"
    res = requests.patch(url, headers=headers, json=payload)
    return {"status": res.status_code, "data": res.json()}
@tool
class Done(BaseModel):
    """Task has been completed."""
    done: bool
      
      
# Setup tools - Fixed list with all tools
tools = [
    create_page, 
    notify_user, 
    read_database_metadata, 
    retrive_database_data,  # Fixed typo
    create_block, 
    update_block, 
    retreve_block_children,  # Fixed typo
    upadate_page,  # Fixed typo
    search_pages,  # Added missing tool
    create_page_with_validation,  # Added missing tool
    create_task_block,  # Added missing tool
    Done
]
tools_by_name = {tool.name: tool for tool in tools}

model_with_tools = llm.bind_tools(tools, tool_choice="any", parallel_tool_calls=False)

def llm_call(state: State):
    """LLM decides whether to call a tool or not"""
    output = {
        "messages": [
            model_with_tools.invoke(
                [
                    {"role": "system", "content": agent_system_prompt.format(
                        tools_prompt=AGENT_TOOLS_PROMPT,
                        background=default_background,
                        response_preferences=default_response_preferences,
                        )
                    },
                    
                ]
                + state["messages"]
            )
        ]
    }
    return output


def tool_handler(state: State):
    """Performs the tool call."""

    # List for tool messages
    result = []
    
    # Iterate through tool calls
    for tool_call in state["messages"][-1].tool_calls:
        # Get the tool
        tool = tools_by_name[tool_call["name"]]
        # Run it
        observation = tool.invoke(tool_call["args"])
        # Create a tool message
        result.append({"role": "tool", "content": observation, "tool_call_id": tool_call["id"]})
    
    # Add it to our messages
    return {"messages": result}


def should_continue(state: State) -> Literal["tool_handler", "__end__"]:
    """Route to tool handler, or end if Done tool called."""

    # Get the last message
    messages = state["messages"]
    last_message = messages[-1]

    # Check if there are tool calls
    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
        for tool_call in last_message.tool_calls:
            if tool_call["name"] == "Done":
                print("✅ Done tool called - ending workflow")
                return END
            else:
                print(f"🔧 Tool call found: {tool_call['name']} - routing to tool_handler")
                return "tool_handler"
    else:
        # No tool calls, end the workflow
        print("🏁 No tool calls found - ending workflow")
        return END


def create_workflow():
    """Create and compile the workflow"""
    
    # Build workflow
    workflow = StateGraph(State)

    # Add nodes
    workflow.add_node("llm_call", llm_call)
    workflow.add_node("tool_handler", tool_handler)

    # Add edges
    workflow.add_edge(START, "llm_call")
    workflow.add_conditional_edges(
        "llm_call",
        should_continue,
        {
            "tool_handler": "tool_handler",
            END: END,
        },
    )
    workflow.add_edge("tool_handler", "llm_call")

    # Compile the agent
    agent = workflow.compile()
    
    # Create overall workflow (using Command pattern like email_assistant.py)
    overall_workflow = (
        StateGraph(State)
        .add_node(triage_router)
        .add_node("action_agent", agent)
        .add_edge(START, "triage_router")
    ).compile()
    
    return overall_workflow


async def process_message(message_text: str, workflow, config):
    """Process a single message through the workflow"""
    try:
        response = await workflow.ainvoke({"post_input": message_text}, config)

        # Print results
        print("\n=== Workflow Results ===")
        if "messages" in response:
            for m in response["messages"]:
                m.pretty_print()

        return response

    except Exception as e:
        print(f"Error running workflow: {e}")
        return None


async def main():
    """Main function to run the workflow with Telegram integration"""
    client = None
    try:
        # Create workflow
        workflow = create_workflow()
        config = {"configurable": {"thread_id": "1"}}

        # Initialize Telegram client
        client = await get_client()
        bot = TeleBot(client)

        channel_link = "ocutesting"

        # Get the channel entity
        channel_entity = await client.get_entity(channel_link)
        print(f"✅ Connected to channel: {channel_entity.title}")

        # Setup message handler with workflow integration
        @client.on(events.NewMessage(chats=channel_entity))
        async def message_handler(event):

            message = event.message
            message_dict = message.to_dict()

            print(message_dict.keys())
            
            if message.text:  # Only process text messages
                print(f"🔔 New message from {channel_entity.title}:")
                print(f"📝 {message.text}")
                print(f"⏰ {message.date}")
                print("-" * 50)

                # Process the message through the workflow
                # await process_message(message, workflow, config)
                print("=" * 50)

        print("🎧 Message listener started. Waiting for new messages...")
        print("Press Ctrl+C to stop")

        # Keep the client running
        await client.run_until_disconnected()

    except KeyboardInterrupt:
        print("\n👋 Stopping listener...")
    except Exception as e:
        print(f"❌ Main error: {e}")
    finally:
        if client:
            await client.disconnect()


async def run_sample_test():
    """Run a sample test without Telegram integration"""
    # Sample post data for testing
    sample_post = """
     Opensea Airdrop — New tasks are Live ✔️

    🔗 Link — https://opensea.io/rewards

    1️⃣. Buy NFT from a Verified Collection on any chain [ Minimum $5 USD ]
    ✨  50 XP

    🖼 NFT Collection [ Base Chain - Cost $17 ]  — https://opensea.io/collection/dxterminal

    🟢 Buy this NFT
    🟢 Claim XP
    🟢 Sell NFT
    ✅ Done
    """

    workflow = create_workflow()
    config = {"configurable": {"thread_id": "1"}}

    print("Testing workflow with sample data...")
    await process_message(sample_post, workflow, config)


if __name__ == "__main__":
    import asyncio

    # Uncomment the line below to run sample test instead of Telegram integration
    # asyncio.run(run_sample_test())

    # Run with Telegram integration
    asyncio.run(main())
